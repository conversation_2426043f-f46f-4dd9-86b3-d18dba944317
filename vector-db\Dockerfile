# Vector Database Service - Chroma DB
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    CHROMA_HOST=0.0.0.0 \
    CHROMA_PORT=8000 \
    PERSIST_DIRECTORY=/chroma/data

# Create non-root user for security
RUN groupadd -r chromauser && useradd -r -g chromauser chromauser

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /chroma

# Install Chroma and dependencies
RUN pip install --no-cache-dir \
    chromadb==0.4.18 \
    fastapi==0.104.1 \
    uvicorn[standard]==0.24.0 \
    pydantic==2.5.0

# Create startup script (as root)
RUN echo '#!/bin/bash\n\
echo "Starting Chroma Vector Database..."\n\
echo "Data directory: $PERSIST_DIRECTORY"\n\
echo "Host: $CHROMA_HOST:$CHROMA_PORT"\n\
\n\
# Start Chroma server\n\
exec chroma run --host $CHROMA_HOST --port $CHROMA_PORT --path $PERSIST_DIRECTORY\n\
' > /chroma/start.sh && chmod +x /chroma/start.sh

# Create necessary directories and set permissions
RUN mkdir -p /chroma/data /chroma/logs && \
    chown -R chromauser:chromauser /chroma

# Switch to non-root user
USER chromauser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/heartbeat || exit 1

# Expose port
EXPOSE 8000

# Start the service
CMD ["/chroma/start.sh"]
