name: ai-coding-agent
channels:
  - conda-forge
  - nvidia
  - pytorch
dependencies:
  - python=3.11
  - cudatoolkit=11.8
  - pip
  - pip:
    # Core AI and ML
    - torch>=2.0.0
    - torchvision
    - torchaudio
    - transformers
    - sentence-transformers

    # Vector DB and embeddings
    - chromadb
    - faiss-cpu

    # FastAPI and web
    - fastapi
    - uvicorn[standard]
    - httpx
    - websockets

    # Database and storage
    - sqlalchemy
    - alembic
    - psycopg2-binary

    # AI model management
    - ollama
    - langchain
    - langchain-community
    - langchain-ollama

    # Data processing
    - pandas
    - numpy
    - pydantic>=2.0.0
    - pydantic-settings

    # Development and testing
    - pytest
    - pytest-asyncio
    - pytest-cov
    - black
    - isort
    - mypy

    # Monitoring and logging
    - structlog
    - prometheus-client

    # Utilities
    - python-dotenv
    - click
    - rich
    - typer
