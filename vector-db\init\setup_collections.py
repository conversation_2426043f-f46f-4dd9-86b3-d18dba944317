#!/usr/bin/env python3
"""
Vector Database Initialization Script
Sets up default collections for LTKB (Long-Term Knowledge Base) integration.
"""

import chromadb
import logging
import time
import os
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def wait_for_chroma(host: str = "localhost", port: int = 8000, max_retries: int = 30):
    """Wait for Chroma to be ready."""
    import requests
    
    for attempt in range(max_retries):
        try:
            response = requests.get(f"http://{host}:{port}/api/v1/heartbeat")
            if response.status_code == 200:
                logger.info("✅ Chroma is ready!")
                return True
        except Exception as e:
            logger.info(f"⏳ Waiting for Chroma... (attempt {attempt + 1}/{max_retries})")
            time.sleep(2)
    
    logger.error("❌ Chroma failed to start within timeout")
    return False

def create_default_collections(client: chromadb.Client) -> List[str]:
    """Create default collections for AI Coding Agent."""
    
    collections_config = [
        {
            "name": "ltkb_knowledge",
            "metadata": {
                "description": "Long-term knowledge base for coding patterns and best practices",
                "embedding_model": "nomic-embed-text:v1.5",
                "purpose": "LTKB"
            }
        },
        {
            "name": "project_context",
            "metadata": {
                "description": "Project-specific context and documentation",
                "embedding_model": "mxbai-embed-large",
                "purpose": "STPM"
            }
        },
        {
            "name": "code_snippets",
            "metadata": {
                "description": "Reusable code snippets and templates",
                "embedding_model": "nomic-embed-text:v1.5",
                "purpose": "LTKB"
            }
        },
        {
            "name": "agent_memory",
            "metadata": {
                "description": "Agent conversation history and learned patterns",
                "embedding_model": "mxbai-embed-large",
                "purpose": "STPM"
            }
        },
        {
            "name": "documentation",
            "metadata": {
                "description": "Technical documentation and API references",
                "embedding_model": "nomic-embed-text:v1.5",
                "purpose": "LTKB"
            }
        }
    ]
    
    created_collections = []
    
    for config in collections_config:
        try:
            # Check if collection already exists
            existing_collections = [col.name for col in client.list_collections()]
            
            if config["name"] in existing_collections:
                logger.info(f"📁 Collection '{config['name']}' already exists")
                created_collections.append(config["name"])
                continue
            
            # Create new collection
            collection = client.create_collection(
                name=config["name"],
                metadata=config["metadata"]
            )
            
            logger.info(f"✅ Created collection: {config['name']}")
            created_collections.append(config["name"])
            
        except Exception as e:
            logger.error(f"❌ Failed to create collection '{config['name']}': {e}")
    
    return created_collections

def setup_vector_database():
    """Main setup function."""
    logger.info("🚀 Starting Vector Database Setup...")
    
    # Configuration
    chroma_host = os.getenv("CHROMA_HOST", "localhost")
    chroma_port = int(os.getenv("CHROMA_PORT", "8000"))
    
    # Wait for Chroma to be ready
    if not wait_for_chroma(chroma_host, chroma_port):
        return False
    
    try:
        # Connect to Chroma
        client = chromadb.HttpClient(
            host=chroma_host,
            port=chroma_port
        )
        
        logger.info(f"🔗 Connected to Chroma at {chroma_host}:{chroma_port}")
        
        # Create default collections
        created_collections = create_default_collections(client)
        
        logger.info(f"📊 Setup complete! Created {len(created_collections)} collections:")
        for collection_name in created_collections:
            logger.info(f"  - {collection_name}")
        
        # Verify setup
        all_collections = client.list_collections()
        logger.info(f"📁 Total collections in database: {len(all_collections)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Vector database setup failed: {e}")
        return False

if __name__ == "__main__":
    success = setup_vector_database()
    exit(0 if success else 1)
