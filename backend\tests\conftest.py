"""
Test configuration and fixtures for AI Coding Agent.

This module provides test configuration, database setup,
and common fixtures for testing.
"""

import os
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

# Set test environment before importing app
os.environ["ENVIRONMENT"] = "testing"
os.environ["SECRET_KEY"] = "test_secret_key_that_is_at_least_32_characters_long"
os.environ["DB_PASSWORD"] = "test_password"

from ai_coding_agent.main import create_app
from ai_coding_agent.models import Base, get_db


# Create test database engine
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest.fixture(scope="function")
def test_db():
    """Create a fresh database for each test."""
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def app():
    """Create a test FastAPI application."""
    test_app = create_app()
    test_app.dependency_overrides[get_db] = override_get_db
    return test_app


@pytest.fixture(scope="function")
def client(app, test_db):
    """Create a test client for the FastAPI application."""
    with TestClient(app) as test_client:
        yield test_client


# Orchestrator test fixtures
@pytest.fixture
def basic_orchestrator_config():
    """Basic configuration for orchestrator testing."""
    return {
        "providers": {
            "ollama": {
                "host": "http://localhost:11434",
                "models": {
                    "test-model-fast": {
                        "role": "test",
                        "agents": ["test_agent"],
                        "description": "Fast test model",
                        "performance": "fast"
                    },
                    "test-model-quality": {
                        "role": "test",
                        "agents": ["test_agent"],
                        "description": "High quality test model",
                        "performance": "high_quality"
                    }
                }
            }
        },
        "routing": {
            "test_agent": {
                "primary": "test-model-fast",
                "secondary": "test-model-quality",
                "fallback": "test-model-fast"
            }
        },
        "load_balancing": {
            "strategy": "round_robin",
            "health_weight": 0.4,
            "performance_weight": 0.3,
            "availability_weight": 0.3
        },
        "model_analytics": {
            "track_performance": True,
            "track_quality_scores": True
        },
        "performance_settings": {
            "request_timeout": 30,
            "retry_attempts": 2,
            "backoff_factor": 1.5
        },
        "quality_thresholds": {
            "minimum_confidence": 0.7,
            "context_relevance": 0.8
        }
    }


@pytest.fixture
def mock_successful_ollama_response():
    """Mock a successful Ollama API response."""
    return {
        "response": """
        Here's a complete solution:

        ```python
        def example_function():
            return "Hello, World!"
        ```

        This function demonstrates a simple implementation.
        Next steps: Add error handling and tests.
        Dependencies: python >= 3.8
        """
    }


@pytest.fixture
def mock_task_context():
    """Create a mock task context for testing."""
    from ai_coding_agent.orchestrator import TaskContext
    return TaskContext(
        project_id="test_project_123",
        user_id="test_user_456",
        session_id="test_session_789",
        ltkb_context="Sample LTKB knowledge context",
        stpm_context="Sample project memory context"
    )


@pytest.fixture
def orchestrator_test_tasks():
    """Sample tasks for testing orchestrator functionality."""
    return {
        "simple": "Fix a typo in the documentation",
        "moderate": "Create a React component for user authentication",
        "complex": "Design a microservices architecture with authentication and authorization",
        "expert": "Implement a distributed system with eventual consistency, CQRS, and event sourcing patterns"
    }
