services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-coding-agent-backend
    ports:
      - "8000:8000"
    volumes:
      # Mount source code for hot reloading during development
      - ./backend/src/ai_coding_agent:/app/ai_coding_agent
      - ./backend/requirements.txt:/app/requirements.txt
      # Mount config and data directories
      - ./backend/config:/app/config
      - ./data/logs:/app/logs
      - ./data/vector_db:/app/vector_db
    env_file:
      - .env
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - HOST=0.0.0.0
      - PORT=8000
      - PYTHONPATH=/app
      # Security keys from .env
      - SECRET_KEY=${SECRET_KEY}
      - CONFIG_ENCRYPTION_KEY=${CONFIG_ENCRYPTION_KEY}
      # Database configuration
      - DATABASE_URL=postgresql://agent:${DB_PASSWORD:-password}@db:5432/ai_coding_agent
      # Ollama configuration for Docker
      - OLLAMA_HOST=http://host.docker.internal:11434
    depends_on:
      db:
        condition: service_healthy
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ai-coding-agent-frontend
    ports:
      - "3000:80"
    volumes:
      # Mount source code for development (optional - for development mode)
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://127.0.0.1:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  db:
    image: postgres:15-alpine
    container_name: ai-coding-agent-db
    environment:
      - POSTGRES_DB=ai_coding_agent
      - POSTGRES_USER=agent
      - POSTGRES_PASSWORD=${DB_PASSWORD:-password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U agent -d ai_coding_agent"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  ai-coding-agent-network:
    driver: bridge

volumes:
  backend-data:
  frontend-data:
  postgres_data:
