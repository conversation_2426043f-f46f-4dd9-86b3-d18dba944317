# AI Coding Agent - Multi-Service Restructuring Plan

## **Target Structure (Best Practices)**

```
ai-coding-agent/
├── backend/                           # Backend service
│   ├── src/ai_coding_agent/          # Application source code
│   ├── Dockerfile                    # Backend container
│   ├── requirements.txt              # Python dependencies
│   ├── .dockerignore                 # Build context exclusions
│   └── config/                       # Backend configurations
│
├── frontend/                         # Frontend service
│   ├── src/                          # React source code
│   ├── public/                       # Static assets
│   ├── Dockerfile                    # Frontend container (multi-stage)
│   ├── package.json                  # Node dependencies
│   ├── .dockerignore                 # Build context exclusions
│   └── nginx.conf                    # Nginx configuration
│
├── vector-db/                        # Vector database service
│   ├── Dockerfile                    # Custom vector DB container
│   ├── init/                         # Database initialization
│   └── data/                         # Database data (volume mount)
│
├── user-projects/                    # User project storage (volume mount)
│   ├── user-{id}/                    # Individual user directories
│   └── templates/                    # Project templates
│
├── infrastructure/                   # Infrastructure configs
│   ├── nginx/                        # Reverse proxy configs
│   ├── ssl/                          # SSL certificates
│   └── monitoring/                   # Monitoring configs
│
├── docker-compose.yml                # Main orchestration
├── docker-compose.dev.yml            # Development overrides
├── .env                              # Environment variables
├── .env.example                      # Environment template
├── .gitignore                        # Git exclusions
└── README.md                         # Project documentation
```

## **Implementation Steps**

### **Step 1: Create Service Directories**
- [x] Create backend/, frontend/, vector-db/, user-projects/
- [x] Move existing code to proper locations
- [x] Create infrastructure/ for shared configs

### **Step 2: Backend Service Setup**
- [x] Create backend/Dockerfile (Python 3.11-slim with multi-stage build)
- [x] Create backend/.dockerignore
- [x] Update backend/requirements.txt
- [x] Configure volume mounts for user projects

### **Step 3: Frontend Service Setup**
- [x] Frontend/Dockerfile already exists (multi-stage build)
- [x] Frontend/.dockerignore already exists
- [x] Nginx configuration already configured
- [x] Hot-reload for development configured

### **Step 4: Vector Database Service**
- [x] Create vector-db/Dockerfile (Chroma configuration)
- [x] Set up Chroma configuration with initialization
- [x] Configure persistent storage with named volumes

### **Step 5: Docker Orchestration**
- [x] Create production docker-compose.yml
- [x] Create development docker-compose.dev.yml
- [x] Configure named volumes for persistence
- [x] Set up service networking with custom bridge

### **Step 6: User Project Management**
- [x] Create user-projects/ volume structure
- [x] Implement user isolation with directory structure
- [x] Set up project templates framework
- [x] Configure backup strategies documentation

## **Key Principles Applied**

1. **Service Isolation**: Each service in its own directory
2. **Data Persistence**: User projects stored in named volumes
3. **Security**: Non-root containers, isolated networks
4. **Scalability**: Easy to add new services
5. **Development**: Hot-reload and debugging support
6. **Production**: Optimized builds and resource limits

## **✅ RESTRUCTURING COMPLETE**

### **What Was Accomplished**

1. **✅ Fixed Package Configuration**: Updated `pyproject.toml` to reference `backend/src/ai_coding_agent`
2. **✅ Updated All Import Paths**: Fixed imports in tests, scripts, and examples
3. **✅ Created Docker Orchestration**: Complete `docker-compose.yml` with all services
4. **✅ Fixed Backend Dockerfile**: Multi-stage build with correct paths
5. **✅ Created Vector Database Service**: Chroma DB with initialization
6. **✅ Set Up Persistent Volumes**: User projects, logs, and database data
7. **✅ Created .dockerignore Files**: Optimized build contexts for all services
8. **✅ Updated Environment Config**: Multi-service `.env.example` with Docker service names

### **Quick Start Commands**

```bash
# 1. Run verification script
python scripts/setup/verify_docker_setup.py

# 2. Quick setup (automated)
bash scripts/setup/quick_start.sh

# 3. Manual setup
cp .env.example .env
# Edit .env with your values
docker-compose up -d

# 4. Check services
docker-compose ps
curl http://localhost:8000/api/v1/health
```

### **Service URLs**

- **Frontend**: <http://localhost:3000>
- **Backend API**: <http://localhost:8000>
- **Vector DB**: <http://localhost:8001>
- **Ollama**: <http://localhost:11434>
- **PostgreSQL**: localhost:5432

The AI Coding Agent is now properly structured for multi-service Docker deployment! 🎉
