#!/usr/bin/env python3
"""
SQLite database setup for roadmap system - Easy testing without PostgreSQL.
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Import our models
from ai_coding_agent.models import Base
from ai_coding_agent.models.roadmap import Project, Roadmap, Phase, Step, Task


def setup_sqlite_database():
    """Create a SQLite database for testing the roadmap system."""
    print("🗄️ Setting up SQLite database for roadmap system...")

    # Create SQLite database file
    db_path = "roadmap_test.db"
    if os.path.exists(db_path):
        print(f"🗑️ Removing existing database: {db_path}")
        os.remove(db_path)

    # Create engine with SQLite
    engine = create_engine(f"sqlite:///{db_path}", echo=True)

    # Create all tables
    print("📋 Creating tables...")
    Base.metadata.create_all(engine)

    # Create session factory
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    print("✅ SQLite database created successfully!")
    print(f"📁 Database file: {db_path}")
    print("📋 Tables created:")
    print("  - projects")
    print("  - roadmaps")
    print("  - phases")
    print("  - steps")
    print("  - tasks")

    return engine, SessionLocal


def test_database_operations(SessionLocal):
    """Test basic database operations."""
    print("\n🧪 Testing database operations...")

    db = SessionLocal()
    try:
        # Test creating a project
        from ai_coding_agent.models.roadmap import Project, TaskStatus

        project = Project(
            name="Test Bakery Website",
            description="A sample project to test the roadmap system",
            tech_stack={"frontend": "React", "backend": "FastAPI"},
            project_rules={"code_style": "PEP8", "testing": "pytest"}
        )

        db.add(project)
        db.commit()
        db.refresh(project)

        print(f"✅ Created project: {project.name} (ID: {project.id})")

        # Test querying
        projects = db.query(Project).all()
        print(f"✅ Found {len(projects)} project(s) in database")

        return True

    except Exception as e:
        print(f"❌ Database operation failed: {e}")
        db.rollback()
        return False
    finally:
        db.close()


if __name__ == "__main__":
    engine, SessionLocal = setup_sqlite_database()

    # Test the database
    if test_database_operations(SessionLocal):
        print("\n🎉 SQLite database is working perfectly!")
        print("💡 You can now use this for testing the roadmap system")
    else:
        print("\n❌ Database test failed")
