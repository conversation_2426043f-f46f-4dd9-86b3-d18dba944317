#!/usr/bin/env python3
"""
AI Coding Agent - Vector Database Initialization Script

This script initializes the Chroma vector database with the required collections
for LTKB (Long-Term Knowledge Base) and STPM (Short-Term Project Memory).

Collections:
- ltkb_documents: Long-term knowledge documents and best practices
- ltkb_code_patterns: Reusable code patterns and templates
- stpm_project_context: Short-term project-specific context
- stpm_conversation_history: AI conversation history and context
- embeddings_metadata: Metadata for all embeddings
"""

import os
import sys
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

try:
    import chromadb
    from chromadb.config import Settings
    from chromadb import PersistentClient
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    print("ChromaDB not available. Please install: pip install chromadb")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
CHROMA_HOST = os.getenv('CHROMA_HOST', 'localhost')
CHROMA_PORT = int(os.getenv('CHROMA_PORT', '8000'))
PERSIST_DIRECTORY = os.getenv('PERSIST_DIRECTORY', '/chroma/data')
CHROMA_AUTH_TOKEN = os.getenv('CHROMA_AUTH_TOKEN', 'default-token')

# Collection configurations
COLLECTIONS_CONFIG = {
    "ltkb_documents": {
        "description": "Long-term knowledge base documents and best practices",
        "metadata": {
            "hnsw:space": "cosine",
            "hnsw:construction_ef": 200,
            "hnsw:M": 16
        },
        "embedding_function": "nomic-embed-text:v1.5"
    },
    "ltkb_code_patterns": {
        "description": "Reusable code patterns, templates, and architectural solutions",
        "metadata": {
            "hnsw:space": "cosine",
            "hnsw:construction_ef": 200,
            "hnsw:M": 16
        },
        "embedding_function": "nomic-embed-text:v1.5"
    },
    "stpm_project_context": {
        "description": "Short-term project-specific context and state",
        "metadata": {
            "hnsw:space": "cosine",
            "hnsw:construction_ef": 100,
            "hnsw:M": 8
        },
        "embedding_function": "mxbai-embed-large"
    },
    "stpm_conversation_history": {
        "description": "AI conversation history and contextual memory",
        "metadata": {
            "hnsw:space": "cosine",
            "hnsw:construction_ef": 100,
            "hnsw:M": 8
        },
        "embedding_function": "mxbai-embed-large"
    },
    "embeddings_metadata": {
        "description": "Metadata and indexing for all embeddings across collections",
        "metadata": {
            "hnsw:space": "cosine",
            "hnsw:construction_ef": 50,
            "hnsw:M": 4
        },
        "embedding_function": "nomic-embed-text:v1.5"
    }
}

class ChromaDBInitializer:
    """Initialize and configure ChromaDB collections for AI Coding Agent."""
    
    def __init__(self):
        """Initialize the ChromaDB client."""
        self.client = None
        self.collections = {}
        
    def connect(self) -> bool:
        """Connect to ChromaDB instance."""
        try:
            # Create client with authentication if available
            if CHROMA_AUTH_TOKEN and CHROMA_AUTH_TOKEN != 'default-token':
                settings = Settings(
                    chroma_server_auth_credentials_provider="chromadb.auth.token.TokenAuthCredentialsProvider",
                    chroma_server_auth_credentials=CHROMA_AUTH_TOKEN,
                    chroma_server_host=CHROMA_HOST,
                    chroma_server_http_port=CHROMA_PORT,
                    persist_directory=PERSIST_DIRECTORY
                )
            else:
                settings = Settings(
                    chroma_server_host=CHROMA_HOST,
                    chroma_server_http_port=CHROMA_PORT,
                    persist_directory=PERSIST_DIRECTORY
                )
            
            self.client = chromadb.HttpClient(
                host=CHROMA_HOST,
                port=CHROMA_PORT,
                settings=settings
            )
            
            # Test connection
            self.client.heartbeat()
            logger.info(f"Successfully connected to ChromaDB at {CHROMA_HOST}:{CHROMA_PORT}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to ChromaDB: {e}")
            return False
    
    def create_collection(self, name: str, config: Dict[str, Any]) -> bool:
        """Create a collection with the specified configuration."""
        try:
            # Check if collection already exists
            try:
                existing_collection = self.client.get_collection(name)
                logger.info(f"Collection '{name}' already exists")
                self.collections[name] = existing_collection
                return True
            except Exception:
                # Collection doesn't exist, create it
                pass
            
            # Create new collection
            collection = self.client.create_collection(
                name=name,
                metadata=config.get("metadata", {}),
                embedding_function=None  # We'll handle embeddings externally
            )
            
            self.collections[name] = collection
            logger.info(f"Created collection '{name}' with configuration: {config['description']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create collection '{name}': {e}")
            return False
    
    def initialize_collections(self) -> bool:
        """Initialize all required collections."""
        logger.info("Initializing ChromaDB collections...")
        
        success_count = 0
        total_count = len(COLLECTIONS_CONFIG)
        
        for collection_name, config in COLLECTIONS_CONFIG.items():
            if self.create_collection(collection_name, config):
                success_count += 1
            else:
                logger.error(f"Failed to initialize collection: {collection_name}")
        
        if success_count == total_count:
            logger.info(f"Successfully initialized all {total_count} collections")
            return True
        else:
            logger.error(f"Only {success_count}/{total_count} collections initialized successfully")
            return False
    
    def seed_initial_data(self) -> bool:
        """Seed collections with initial data if needed."""
        logger.info("Seeding initial data...")
        
        try:
            # Seed LTKB with basic AI coding patterns
            ltkb_patterns = self.collections.get("ltkb_code_patterns")
            if ltkb_patterns:
                initial_patterns = [
                    {
                        "id": "pattern_001",
                        "document": "FastAPI REST API pattern with Pydantic models and dependency injection",
                        "metadata": {
                            "category": "api_pattern",
                            "language": "python",
                            "framework": "fastapi",
                            "complexity": "intermediate",
                            "created_at": datetime.now().isoformat()
                        }
                    },
                    {
                        "id": "pattern_002", 
                        "document": "React component pattern with TypeScript, hooks, and context",
                        "metadata": {
                            "category": "frontend_pattern",
                            "language": "typescript",
                            "framework": "react",
                            "complexity": "intermediate",
                            "created_at": datetime.now().isoformat()
                        }
                    },
                    {
                        "id": "pattern_003",
                        "document": "Docker multi-stage build pattern for Python applications",
                        "metadata": {
                            "category": "deployment_pattern",
                            "technology": "docker",
                            "language": "python",
                            "complexity": "advanced",
                            "created_at": datetime.now().isoformat()
                        }
                    }
                ]
                
                # Add initial patterns (only if collection is empty)
                try:
                    existing_count = ltkb_patterns.count()
                    if existing_count == 0:
                        ltkb_patterns.add(
                            documents=[p["document"] for p in initial_patterns],
                            metadatas=[p["metadata"] for p in initial_patterns],
                            ids=[p["id"] for p in initial_patterns]
                        )
                        logger.info(f"Seeded {len(initial_patterns)} initial code patterns")
                    else:
                        logger.info(f"Collection already has {existing_count} patterns, skipping seeding")
                except Exception as e:
                    logger.warning(f"Could not seed initial patterns: {e}")
            
            # Seed metadata collection with schema information
            metadata_collection = self.collections.get("embeddings_metadata")
            if metadata_collection:
                schema_docs = [
                    {
                        "id": "schema_ltkb_documents",
                        "document": "Schema for LTKB documents collection",
                        "metadata": {
                            "type": "schema",
                            "collection": "ltkb_documents",
                            "fields": ["document", "category", "tags", "source", "created_at", "updated_at"],
                            "created_at": datetime.now().isoformat()
                        }
                    },
                    {
                        "id": "schema_stpm_context",
                        "document": "Schema for STPM project context collection",
                        "metadata": {
                            "type": "schema",
                            "collection": "stpm_project_context",
                            "fields": ["document", "project_id", "context_type", "relevance_score", "created_at"],
                            "created_at": datetime.now().isoformat()
                        }
                    }
                ]
                
                try:
                    existing_count = metadata_collection.count()
                    if existing_count == 0:
                        metadata_collection.add(
                            documents=[d["document"] for d in schema_docs],
                            metadatas=[d["metadata"] for d in schema_docs],
                            ids=[d["id"] for d in schema_docs]
                        )
                        logger.info(f"Seeded {len(schema_docs)} schema documents")
                    else:
                        logger.info(f"Metadata collection already has {existing_count} documents")
                except Exception as e:
                    logger.warning(f"Could not seed schema documents: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to seed initial data: {e}")
            return False
    
    def validate_setup(self) -> bool:
        """Validate that all collections are properly set up."""
        logger.info("Validating ChromaDB setup...")
        
        try:
            # Check all collections exist and are accessible
            for collection_name in COLLECTIONS_CONFIG.keys():
                collection = self.client.get_collection(collection_name)
                count = collection.count()
                logger.info(f"Collection '{collection_name}': {count} documents")
            
            # Test basic operations
            test_collection = self.collections.get("stpm_project_context")
            if test_collection:
                # Test add/query/delete operations
                test_id = f"test_{datetime.now().timestamp()}"
                test_collection.add(
                    documents=["Test document for validation"],
                    metadatas=[{"type": "test", "created_at": datetime.now().isoformat()}],
                    ids=[test_id]
                )
                
                # Query the test document
                results = test_collection.query(
                    query_texts=["Test document"],
                    n_results=1
                )
                
                if results and len(results['ids'][0]) > 0:
                    logger.info("Basic query operation successful")
                    
                    # Clean up test document
                    test_collection.delete(ids=[test_id])
                    logger.info("Test document cleaned up")
                else:
                    logger.warning("Query operation returned no results")
            
            logger.info("ChromaDB setup validation completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Setup validation failed: {e}")
            return False
    
    def generate_status_report(self) -> Dict[str, Any]:
        """Generate a status report of the ChromaDB setup."""
        report = {
            "timestamp": datetime.now().isoformat(),
            "chroma_host": CHROMA_HOST,
            "chroma_port": CHROMA_PORT,
            "persist_directory": PERSIST_DIRECTORY,
            "collections": {},
            "total_documents": 0,
            "status": "unknown"
        }
        
        try:
            for collection_name in COLLECTIONS_CONFIG.keys():
                collection = self.client.get_collection(collection_name)
                count = collection.count()
                report["collections"][collection_name] = {
                    "document_count": count,
                    "status": "active"
                }
                report["total_documents"] += count
            
            report["status"] = "healthy"
            
        except Exception as e:
            report["status"] = f"error: {str(e)}"
        
        return report

def main():
    """Main initialization function."""
    logger.info("Starting ChromaDB initialization for AI Coding Agent")
    
    # Initialize ChromaDB
    initializer = ChromaDBInitializer()
    
    # Connect to ChromaDB
    if not initializer.connect():
        logger.error("Failed to connect to ChromaDB")
        sys.exit(1)
    
    # Initialize collections
    if not initializer.initialize_collections():
        logger.error("Failed to initialize collections")
        sys.exit(1)
    
    # Seed initial data
    if not initializer.seed_initial_data():
        logger.warning("Failed to seed initial data (continuing anyway)")
    
    # Validate setup
    if not initializer.validate_setup():
        logger.error("Setup validation failed")
        sys.exit(1)
    
    # Generate and save status report
    report = initializer.generate_status_report()
    report_path = Path(PERSIST_DIRECTORY) / "initialization_report.json"
    report_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Initialization completed successfully. Report saved to: {report_path}")
    logger.info(f"Total collections: {len(report['collections'])}")
    logger.info(f"Total documents: {report['total_documents']}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
