#!/usr/bin/env python3
"""
Comprehensive test for all database systems:
1. Hybrid Database (SQLite + Supabase)
2. Vector Database (Chroma)
3. Integration testing
"""

import os
import sys
from datetime import datetime

def test_hybrid_database():
    """Test the hybrid database setup."""
    print("🧪 Testing Hybrid Database System")
    print("=" * 50)

    try:
        from src.ai_coding_agent.models.base import get_hybrid_db_manager
        print("✅ Successfully imported hybrid database manager")

        manager = get_hybrid_db_manager()
        print("✅ Manager created successfully")

        # Test configuration
        print(f"\n📊 Configuration:")
        print(f"  Mode: {manager.mode}")
        print(f"  Local tables: {len(manager.local_tables)} tables")
        print(f"  Supabase tables: {len(manager.supabase_tables)} tables")

        # Test connection status
        status = manager.get_connection_status()
        print(f"\n🔍 Connection Status:")
        for key, value in status.items():
            status_icon = "✅" if "connected" in str(value) else "⚠️" if "error" in str(value) else "ℹ️"
            print(f"  {status_icon} {key}: {value}")

        # Test table routing
        print(f"\n🎯 Table Routing Test:")
        test_tables = ["projects", "roadmaps", "best_practices", "tech_stack_metadata", "rules", "tasks"]
        for table in test_tables:
            local = manager.is_local_table(table)
            supabase = manager.is_supabase_table(table)
            location = "Local" if local else "Supabase" if supabase else "Unknown"
            print(f"  📋 {table}: {location}")

        # Test local database creation
        print(f"\n🔧 Testing local database operations...")
        try:
            manager.create_local_tables()
            print("✅ Local tables created successfully")
        except Exception as e:
            print(f"⚠️ Local table creation: {e}")

        return True

    except Exception as e:
        print(f"❌ Hybrid Database Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vector_database():
    """Test the vector database setup."""
    print("\n🧪 Testing Vector Database System")
    print("=" * 50)

    try:
        from src.ai_coding_agent.services.vector_db import VectorDBClient
        print("✅ Successfully imported vector database")

        # Check if Chroma is available
        try:
            import chromadb
            print("✅ ChromaDB library available")
        except ImportError:
            print("❌ ChromaDB library not installed")
            return False

        # Initialize vector database
        vector_db = VectorDBClient()
        print("✅ Vector database initialized")

        # Test connection
        try:
            # Test basic operations
            print(f"📊 Vector DB initialized with directory: {vector_db.persist_directory}")

            # Check collections
            if hasattr(vector_db, 'collections') and vector_db.collections:
                print(f"✅ Collections available: {list(vector_db.collections.keys())}")
            else:
                print("ℹ️ No collections found (this is normal for new setup)")

            # Test embedding configuration
            if hasattr(vector_db, 'embedding_configs'):
                print(f"✅ Embedding configs loaded: {len(vector_db.embedding_configs)} configs")

            return True

        except Exception as e:
            print(f"⚠️ Vector database operations: {e}")
            return False

    except Exception as e:
        print(f"❌ Vector Database Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_integration():
    """Test integration between databases."""
    print("\n🧪 Testing Database Integration")
    print("=" * 50)

    try:
        # Test importing both systems
        from src.ai_coding_agent.models.base import get_hybrid_db_manager
        from src.ai_coding_agent.services.vector_db import VectorDBClient

        hybrid_manager = get_hybrid_db_manager()
        vector_db = VectorDBClient()

        print("✅ Both database systems imported successfully")

        # Test project workflow simulation
        print("\n🔄 Simulating project workflow:")

        # 1. Hybrid DB: Create project structure
        print("  📋 Step 1: Project structure (Hybrid DB)")
        project_data = {
            "name": "Test Project",
            "description": "A test project for database integration",
            "created_at": datetime.now().isoformat()
        }
        print(f"    Project: {project_data['name']}")

        # 2. Vector DB: Store project knowledge
        print("  🧠 Step 2: Project knowledge (Vector DB)")
        print(f"    Vector DB directory: {vector_db.persist_directory}")
        print(f"    Collections: {list(vector_db.collections.keys()) if hasattr(vector_db, 'collections') else 'Not loaded'}")

        # 3. Integration point: Search and relate
        print("  🔍 Step 3: Cross-database operations")
        print("    ✅ Both databases accessible from same context")
        print("    ✅ Ready for async operations and embeddings")
        print("    ℹ️ Full embedding tests require async context")

        print("✅ Database integration test completed")
        return True

    except Exception as e:
        print(f"❌ Integration Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """Check environment configuration."""
    print("🧪 Environment Configuration Check")
    print("=" * 50)

    # Check .env file
    env_file = ".env"
    if os.path.exists(env_file):
        print("✅ .env file found")
    else:
        print("❌ .env file not found")
        return False

    # Check key environment variables
    required_vars = [
        "DATABASE_MODE",
        "SQLITE_DATABASE_URL",
        "SUPABASE_URL",
        "CHROMA_PERSIST_DIRECTORY",
        "EMBEDDING_MODEL"
    ]

    from src.ai_coding_agent.config import settings

    print("\n📋 Configuration values:")
    print(f"  Database mode: {settings.hybrid_db.mode}")
    print(f"  SQLite URL: {settings.hybrid_db.sqlite_url}")
    print(f"  Supabase URL: {settings.supabase.url[:50]}..." if settings.supabase.url else "  Supabase URL: Not set")

    # Check vector db directory
    vector_dir = "./vector_db"
    if os.path.exists(vector_dir):
        print(f"✅ Vector DB directory exists: {vector_dir}")
        files = os.listdir(vector_dir)
        print(f"  Files: {files}")
    else:
        print(f"⚠️ Vector DB directory not found: {vector_dir}")
        print("  Creating directory...")
        os.makedirs(vector_dir, exist_ok=True)

    return True

def main():
    """Run all database tests."""
    print("🚀 AI Coding Agent - Database Systems Test")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    results = {}

    # 1. Environment check
    results['environment'] = check_environment()

    # 2. Hybrid database test
    results['hybrid_db'] = test_hybrid_database()

    # 3. Vector database test
    results['vector_db'] = test_vector_database()

    # 4. Integration test
    results['integration'] = test_database_integration()

    # Summary
    print("\n🏁 Test Summary")
    print("=" * 50)
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.replace('_', ' ').title()}")

    print(f"\n📊 Results: {passed_tests}/{total_tests} tests passed")

    if passed_tests == total_tests:
        print("🎉 All database systems are working correctly!")
    else:
        print("⚠️ Some database systems need attention.")

    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
