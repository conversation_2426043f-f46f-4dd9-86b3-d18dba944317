#!/usr/bin/env python3
"""
Automated development environment setup script.
This script automates the database configuration and validation process.
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import Dict, List

class DevEnvironmentSetup:
    """Automate development environment setup and validation."""

    def __init__(self):
        self.project_root = Path.cwd()
        self.env_file = self.project_root / ".env"
        self.required_dirs = [
            "vector_db",
            "logs",
            "uploads",
            "tests/fixtures"
        ]

    def setup_directories(self):
        """Create required directories."""
        print("🏗️  Setting up project directories...")
        for dir_path in self.required_dirs:
            full_path = self.project_root / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ Created: {dir_path}")

    def validate_environment(self) -> Dict[str, bool]:
        """Validate environment configuration."""
        print("🔍 Validating environment...")

        results = {}

        # Check .env file
        if self.env_file.exists():
            print("   ✅ .env file found")
            results['env_file'] = True
        else:
            print("   ❌ .env file missing")
            results['env_file'] = False

        # Check Python environment
        try:
            import ai_coding_agent.config
            print("   ✅ Configuration module accessible")
            results['config'] = True
        except ImportError as e:
            print(f"   ❌ Configuration import failed: {e}")
            results['config'] = False

        # Check database dependencies
        deps = {
            'sqlalchemy': 'SQLAlchemy',
            'chromadb': 'ChromaDB',
            'pydantic': 'Pydantic'
        }

        for module, name in deps.items():
            try:
                __import__(module)
                print(f"   ✅ {name} available")
                results[module] = True
            except ImportError:
                print(f"   ❌ {name} missing")
                results[module] = False

        return results

    def run_database_tests(self):
        """Run comprehensive database tests."""
        print("🧪 Running database tests...")
        try:
            result = subprocess.run([
                sys.executable, "test_all_databases.py"
            ], capture_output=True, text=True, cwd=self.project_root)

            if result.returncode == 0:
                print("   ✅ All database tests passed")
                return True
            else:
                print("   ❌ Database tests failed:")
                print(result.stdout)
                print(result.stderr)
                return False
        except Exception as e:
            print(f"   ❌ Failed to run tests: {e}")
            return False

    def generate_config_template(self):
        """Generate .env template if missing."""
        if not self.env_file.exists():
            print("📝 Generating .env template...")
            template = '''# AI Coding Agent Environment Configuration
# Database Mode Selection
DATABASE_MODE=sqlite

# SQLite Configuration (for development)
SQLITE_DATABASE_URL=sqlite:///./roadmap_test.db

# PostgreSQL Configuration (for production)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ai_coding_agent
DB_USER=postgres
DB_PASSWORD=your_password_here

# Supabase Configuration (for cloud)
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_key_here

# Vector Database Configuration
CHROMA_PERSIST_DIRECTORY=./vector_db/chroma
EMBEDDING_MODEL=all-MiniLM-L6-v2

# Application Settings
APP_NAME=AI Coding Agent
DEBUG=true
HOST=localhost
PORT=8000
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Security Settings
SECRET_KEY=your_super_secret_key_32_chars_minimum_here
'''
            self.env_file.write_text(template)
            print("   ✅ .env template created")

    def run_setup(self):
        """Run complete setup process."""
        print("🚀 AI Coding Agent - Development Setup")
        print("=" * 50)

        # Step 1: Create directories
        self.setup_directories()

        # Step 2: Generate config if needed
        self.generate_config_template()

        # Step 3: Validate environment
        validation_results = self.validate_environment()

        # Step 4: Run tests if validation passes
        if all(validation_results.values()):
            test_success = self.run_database_tests()

            if test_success:
                print("\n🎉 Development environment setup complete!")
                print("   Ready for development with hybrid database architecture.")
            else:
                print("\n⚠️ Setup complete but tests failed.")
                print("   Please check database configuration.")
        else:
            print("\n⚠️ Environment validation failed.")
            print("   Please install missing dependencies and configure .env file.")

        return validation_results

if __name__ == "__main__":
    setup = DevEnvironmentSetup()
    setup.run_setup()
