# Python
__pycache__/
*.py[cod]
*$py.class
*.so

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs (exclude from build, but will be mounted)
*.log
logs/

# Database data (exclude from build, will be mounted)
data/
*.db
*.sqlite3

# Environment variables
.env*

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Backup files
*.bak
*.backup
