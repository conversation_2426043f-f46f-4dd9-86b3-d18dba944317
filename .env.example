# AI Coding Agent Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# SECURITY CONFIGURATION (REQUIRED)
# =============================================================================

# Application secret key (32+ characters, generate with: openssl rand -hex 32)
SECRET_KEY=your_super_secret_key_that_must_be_at_least_32_characters_long

# Configuration encryption key (32+ characters, generate with: openssl rand -hex 32)
CONFIG_ENCRYPTION_KEY=your_config_encryption_key_32_chars_min

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

APP_NAME=AI Coding Agent
APP_VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000
CORS_ORIGINS=http://localhost:3000,http://localhost:8000

# JWT Configuration
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database (Docker service names for containers)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=ai_coding_agent
DB_USER=agent
DB_PASSWORD=your_secure_database_password_here

# Database URL (constructed from above values)
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}

# =============================================================================
# AI SERVICE CONFIGURATION
# =============================================================================

# Ollama Configuration (Docker service name for containers)
OLLAMA_HOST=http://ollama:11434
OLLAMA_TIMEOUT=300
DEFAULT_AI_MODEL=mistral:7b-instruct-q4_0

# Specialized Agent Models (5 core agents)
ARCHITECT_AGENT_MODEL=deepseek-coder:6.7b-instruct-q4_0
FRONTEND_AGENT_MODEL=starcoder2:3b
BACKEND_AGENT_MODEL=deepseek-coder:6.7b-instruct-q4_0
SHELL_AGENT_MODEL=qwen2.5:3b
ISSUE_FIX_AGENT_MODEL=yi-coder:1.5b

# Code-specific task models
CODE_COMPLETION_MODEL=starcoder2:3b
CODE_GENERATION_MODEL=deepseek-coder:6.7b-instruct
CODE_REVIEW_MODEL=deepseek-coder:6.7b-instruct

# Chat and specialized models
CHAT_MODEL=mistral:7b-instruct-q4_0
DOCUMENTATION_MODEL=llama3.2:3b

# AI Performance settings
AI_MAX_TOKENS=4096
AI_TEMPERATURE=0.7

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================

# Chroma Vector Database (Docker service name for containers)
VECTOR_DB_HOST=vector-db
VECTOR_DB_PORT=8000
CHROMA_HOST=0.0.0.0
CHROMA_PORT=8000
PERSIST_DIRECTORY=/chroma/data

# Embedding Models
LTKB_EMBEDDING_MODEL=nomic-embed-text:v1.5
STPM_EMBEDDING_MODEL=mxbai-embed-large

# =============================================================================
# DOCKER & DEVELOPMENT CONFIGURATION
# =============================================================================

# Docker Compose Project Name
COMPOSE_PROJECT_NAME=ai-coding-agent

# Development Frontend URL
REACT_APP_API_URL=http://localhost:8000

# Hot Reload (development only)
RELOAD=true

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================

# Rate Limiting
API_RATE_LIMIT_REQUESTS=100
API_RATE_LIMIT_WINDOW_MINUTES=1
ADMIN_RATE_LIMIT_REQUESTS=20
ADMIN_RATE_LIMIT_WINDOW_MINUTES=5
AUTH_RATE_LIMIT_REQUESTS=5
AUTH_RATE_LIMIT_WINDOW_MINUTES=15

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/Disable Features
ENABLE_LTKB=true
ENABLE_VECTOR_SEARCH=true
ENABLE_AGENT_COLLABORATION=true
ENABLE_PROJECT_TEMPLATES=true
ENABLE_AUDIT_LOGGING=true
