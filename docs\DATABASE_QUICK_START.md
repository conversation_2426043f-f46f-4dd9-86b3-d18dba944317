# Database Architecture Quick Start Guide

## 🚀 Quick Setup (New Developers)

```bash
# 1. Clone and setup
git clone <repository>
cd ai-coding-agent
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows

# 2. Install dependencies
pip install -r requirements.txt

# 3. Automated setup
python dev_setup.py

# 4. Verify everything works
python test_all_databases.py
```

## 🗄️ Database Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SQLite/       │    │    Supabase     │    │   Chroma DB     │
│   PostgreSQL    │    │    (Cloud)      │    │   (Vector)      │
│   (Local)       │    │                 │    │                 │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • roadmaps      │    │ • best_practices│    │ • ltkb          │
│ • rules         │    │ • tech_stack    │    │ • stpm          │
│ • projects      │    │ • security      │    │ • systems       │
│ • phases        │    │ • frameworks    │    │ • projects      │
│ • steps         │    │ • libraries     │    │                 │
│ • tasks         │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Environment Management

```bash
# Switch to development (SQLite)
python config_manager.py switch development

# Switch to testing
python config_manager.py switch testing

# Switch to production (Hybrid)
python config_manager.py switch production

# List available environments
python config_manager.py list
```

## 📊 Database Migrations

```bash
# Check migration status
python migration_manager.py status

# Create new migration
python migration_manager.py create "description" [local|supabase|vector|all]

# Apply migrations
python migration_manager.py apply <migration_id> <database>

# View pending migrations
python migration_manager.py pending local
```

## 🧪 Testing

```bash
# Test all databases
python test_all_databases.py

# Test specific roadmap functionality
python test_roadmap_sqlite.py

# Test hybrid database only
python test_hybrid_db.py
```

## 🔍 Troubleshooting

### Common Issues:

1. **Configuration Errors**: Run `python dev_setup.py` to validate
2. **Database Connection Issues**: Check `.env` file settings
3. **Missing Dependencies**: Run `pip install -r requirements.txt`
4. **Pylance Errors**: Usually fixed by proper virtual environment activation

### Debug Commands:

```bash
# Check configuration
python -c "from src.ai_coding_agent.config import settings; print(settings.hybrid_db.mode)"

# Test database connections
python test_all_databases.py

# Validate environment
python dev_setup.py
```

## 📈 Best Practices

1. **Always use environment switching** instead of manually editing .env
2. **Run tests after configuration changes**
3. **Create migrations for schema changes**
4. **Use SQLite for development, hybrid for production**
5. **Back up .env files before switching environments**

## 🏗️ Architecture Benefits

- **Fast Development**: SQLite requires no setup
- **Scalable Production**: PostgreSQL + Supabase handles load
- **Smart Search**: Vector database enables AI features
- **Flexible Deployment**: Switch between environments easily
- **Data Separation**: Local vs cloud data routing
