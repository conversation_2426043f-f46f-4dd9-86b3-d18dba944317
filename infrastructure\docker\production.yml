# AI Coding Agent - Production Docker Configuration
# Production-optimized Docker Compose configuration with security, scaling, and monitoring

version: '3.8'

services:
  # Backend Service - Production Configuration
  backend:
    build:
      context: ../../backend
      dockerfile: Dockerfile
      target: production
    image: ai-coding-agent/backend:${VERSION:-latest}
    container_name: ai-coding-agent-backend-prod
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      # Production volumes (read-only where possible)
      - user-projects:/app/user-projects
      - backend-logs:/app/logs
      - vector-db-data:/app/vector_db:ro
      - ../../backend/config:/app/config:ro
    environment:
      - ENVIRONMENT=production
      - HOST=0.0.0.0
      - PORT=8000
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      # Security keys from .env
      - SECRET_KEY=${SECRET_KEY}
      - CONFIG_ENCRYPTION_KEY=${CONFIG_ENCRYPTION_KEY}
      # Database configuration
      - DATABASE_URL=postgresql://agent:${DB_PASSWORD}@postgres:5432/ai_coding_agent
      # AI Service configuration
      - OLLAMA_HOST=http://ollama:11434
      - VECTOR_DB_HOST=vector-db
      - VECTOR_DB_PORT=8000
      # Production optimizations
      - WORKERS=4
      - MAX_WORKERS=8
      - WORKER_CONNECTIONS=1000
      - KEEPALIVE=2
    env_file:
      - ../../.env
    networks:
      - ai-coding-agent-network
      - monitoring
    depends_on:
      postgres:
        condition: service_healthy
      vector-db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        labels: "service=backend,environment=production"

  # Frontend Service - Production Configuration
  frontend:
    build:
      context: ../../frontend
      dockerfile: Dockerfile
      target: production
    image: ai-coding-agent/frontend:${VERSION:-latest}
    container_name: ai-coding-agent-frontend-prod
    restart: unless-stopped
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=https://${DOMAIN:-localhost:8000}
      - REACT_APP_WS_URL=wss://${DOMAIN:-localhost:8000}
    networks:
      - ai-coding-agent-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
        labels: "service=frontend,environment=production"

  # Vector Database - Production Configuration
  vector-db:
    build:
      context: ../../vector-db
      dockerfile: Dockerfile
    image: ai-coding-agent/vector-db:${VERSION:-latest}
    container_name: ai-coding-agent-vector-db-prod
    restart: unless-stopped
    ports:
      - "8001:8000"
    volumes:
      - vector-db-data:/chroma/data
      - vector-db-logs:/chroma/logs
    environment:
      - CHROMA_HOST=0.0.0.0
      - CHROMA_PORT=8000
      - PERSIST_DIRECTORY=/chroma/data
      - CHROMA_LOG_LEVEL=INFO
      - CHROMA_SERVER_AUTH_CREDENTIALS_PROVIDER=chromadb.auth.token.TokenAuthCredentialsProvider
      - CHROMA_SERVER_AUTH_CREDENTIALS=${CHROMA_AUTH_TOKEN:-default-token}
    networks:
      - ai-coding-agent-network
      - monitoring
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 8G
        reservations:
          cpus: '1.0'
          memory: 4G
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        labels: "service=vector-db,environment=production"

  # PostgreSQL Database - Production Configuration
  postgres:
    image: postgres:15-alpine
    container_name: ai-coding-agent-postgres-prod
    restart: unless-stopped
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ../../database/init:/docker-entrypoint-initdb.d:ro
    environment:
      - POSTGRES_DB=ai_coding_agent
      - POSTGRES_USER=agent
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
      - POSTGRES_HOST_AUTH_METHOD=scram-sha-256
    networks:
      - ai-coding-agent-network
      - monitoring
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U agent -d ai_coding_agent"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        labels: "service=postgres,environment=production"

  # Ollama AI Service - Production Configuration
  ollama:
    image: ollama/ollama:${OLLAMA_VERSION:-latest}
    container_name: ai-coding-agent-ollama-prod
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=http://localhost:*,http://127.0.0.1:*,http://backend:*
    networks:
      - ai-coding-agent-network
      - monitoring
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 16G
        reservations:
          cpus: '2.0'
          memory: 8G
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "5"
        labels: "service=ollama,environment=production"

  # Nginx Reverse Proxy - Production Configuration
  nginx:
    image: nginx:alpine
    container_name: ai-coding-agent-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../nginx/ssl.conf:/etc/nginx/conf.d/default.conf:ro
      - ../ssl/certs:/etc/nginx/ssl:ro
      - /var/www/certbot:/var/www/certbot:ro
    networks:
      - ai-coding-agent-network
      - monitoring
    depends_on:
      - backend
      - frontend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        labels: "service=nginx,environment=production"

# Networks
networks:
  ai-coding-agent-network:
    driver: bridge
    name: ai-coding-agent-prod-network
    ipam:
      config:
        - subnet: **********/16
  monitoring:
    external: true
    name: ai-coding-agent-monitoring

# Named Volumes for Data Persistence
volumes:
  user-projects:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ../../user-projects
  postgres-data:
    driver: local
  vector-db-data:
    driver: local
  vector-db-logs:
    driver: local
  backend-logs:
    driver: local
  ollama-data:
    driver: local

# Production deployment configuration
x-deploy-labels: &deploy-labels
  - "traefik.enable=true"
  - "traefik.docker.network=ai-coding-agent-prod-network"

x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "100m"
    max-file: "5"

x-restart-policy: &restart-policy
  restart_policy:
    condition: on-failure
    delay: 5s
    max_attempts: 3
    window: 120s
