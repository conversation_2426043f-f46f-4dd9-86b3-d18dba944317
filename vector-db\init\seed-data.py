#!/usr/bin/env python3
"""
AI Coding Agent - Vector Database Seed Data Script

This script seeds the ChromaDB collections with initial knowledge base data,
code patterns, and example project contexts for the AI Coding Agent.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any
from datetime import datetime
from pathlib import Path

try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    print("ChromaDB not available. Please install: pip install chromadb")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
CHROMA_HOST = os.getenv('CHROMA_HOST', 'localhost')
CHROMA_PORT = int(os.getenv('CHROMA_PORT', '8000'))
PERSIST_DIRECTORY = os.getenv('PERSIST_DIRECTORY', '/chroma/data')

# Seed data for LTKB documents
LTKB_DOCUMENTS = [
    {
        "id": "ltkb_001",
        "document": "FastAPI Security Best Practices: Always use dependency injection for authentication, implement rate limiting, validate all inputs with Pydantic models, use HTTPS in production, and store secrets in environment variables.",
        "metadata": {
            "category": "security",
            "technology": "fastapi",
            "language": "python",
            "importance": "high",
            "tags": ["security", "authentication", "rate-limiting", "validation"],
            "source": "ai-coding-agent-knowledge",
            "created_at": datetime.now().isoformat()
        }
    },
    {
        "id": "ltkb_002",
        "document": "React Performance Optimization: Use React.memo for component memoization, implement useMemo and useCallback for expensive computations, lazy load components with React.lazy, optimize bundle size with code splitting.",
        "metadata": {
            "category": "performance",
            "technology": "react",
            "language": "typescript",
            "importance": "high",
            "tags": ["performance", "optimization", "memoization", "lazy-loading"],
            "source": "ai-coding-agent-knowledge",
            "created_at": datetime.now().isoformat()
        }
    },
    {
        "id": "ltkb_003",
        "document": "Docker Multi-Stage Build Pattern: Use multi-stage builds to reduce image size, separate build and runtime dependencies, copy only necessary files, use .dockerignore to exclude unnecessary files.",
        "metadata": {
            "category": "deployment",
            "technology": "docker",
            "importance": "medium",
            "tags": ["docker", "optimization", "deployment", "containerization"],
            "source": "ai-coding-agent-knowledge",
            "created_at": datetime.now().isoformat()
        }
    },
    {
        "id": "ltkb_004",
        "document": "Vector Database Best Practices: Use appropriate embedding models for your use case, implement proper indexing strategies, batch operations for better performance, monitor memory usage and query latency.",
        "metadata": {
            "category": "database",
            "technology": "chromadb",
            "importance": "high",
            "tags": ["vector-database", "embeddings", "performance", "indexing"],
            "source": "ai-coding-agent-knowledge",
            "created_at": datetime.now().isoformat()
        }
    },
    {
        "id": "ltkb_005",
        "document": "AI Model Integration Patterns: Implement proper error handling and retries, use connection pooling, implement circuit breakers for resilience, monitor token usage and costs.",
        "metadata": {
            "category": "ai-integration",
            "technology": "ollama",
            "importance": "high",
            "tags": ["ai", "integration", "resilience", "monitoring"],
            "source": "ai-coding-agent-knowledge",
            "created_at": datetime.now().isoformat()
        }
    }
]

# Seed data for code patterns
CODE_PATTERNS = [
    {
        "id": "pattern_001",
        "document": """
FastAPI Dependency Injection Pattern:

```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def get_current_user(token: str = Depends(security)):
    # Validate token and return user
    if not validate_token(token.credentials):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    return get_user_from_token(token.credentials)

@app.get("/protected")
async def protected_route(user = Depends(get_current_user)):
    return {"message": f"Hello {user.username}"}
```
        """,
        "metadata": {
            "pattern_type": "authentication",
            "language": "python",
            "framework": "fastapi",
            "complexity": "intermediate",
            "use_case": "API authentication and authorization",
            "tags": ["fastapi", "dependency-injection", "authentication", "security"],
            "created_at": datetime.now().isoformat()
        }
    },
    {
        "id": "pattern_002",
        "document": """
React Custom Hook Pattern:

```typescript
import { useState, useEffect } from 'react';

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export function useApi<T>(url: string): ApiState<T> {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: true,
    error: null
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));
        const response = await fetch(url);
        if (!response.ok) throw new Error('Failed to fetch');
        const data = await response.json();
        setState({ data, loading: false, error: null });
      } catch (error) {
        setState({ data: null, loading: false, error: error.message });
      }
    };

    fetchData();
  }, [url]);

  return state;
}
```
        """,
        "metadata": {
            "pattern_type": "custom-hook",
            "language": "typescript",
            "framework": "react",
            "complexity": "intermediate",
            "use_case": "API data fetching with loading and error states",
            "tags": ["react", "hooks", "typescript", "api", "state-management"],
            "created_at": datetime.now().isoformat()
        }
    },
    {
        "id": "pattern_003",
        "document": """
Docker Multi-Stage Build Pattern:

```dockerfile
# Build stage
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# Production stage
FROM node:18-alpine AS production
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
WORKDIR /app
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .
USER nextjs
EXPOSE 3000
CMD ["npm", "start"]
```
        """,
        "metadata": {
            "pattern_type": "containerization",
            "technology": "docker",
            "complexity": "intermediate",
            "use_case": "Optimized production container builds",
            "tags": ["docker", "multi-stage", "optimization", "security"],
            "created_at": datetime.now().isoformat()
        }
    },
    {
        "id": "pattern_004",
        "document": """
Vector Database Query Pattern:

```python
from typing import List, Dict, Any
import chromadb

class VectorDBManager:
    def __init__(self, collection_name: str):
        self.client = chromadb.PersistentClient()
        self.collection = self.client.get_or_create_collection(collection_name)
    
    async def semantic_search(
        self, 
        query: str, 
        n_results: int = 5,
        filters: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        results = self.collection.query(
            query_texts=[query],
            n_results=n_results,
            where=filters
        )
        
        return [
            {
                "id": results['ids'][0][i],
                "document": results['documents'][0][i],
                "metadata": results['metadatas'][0][i],
                "distance": results['distances'][0][i]
            }
            for i in range(len(results['ids'][0]))
        ]
```
        """,
        "metadata": {
            "pattern_type": "database-query",
            "language": "python",
            "technology": "chromadb",
            "complexity": "intermediate",
            "use_case": "Semantic search with vector databases",
            "tags": ["vector-database", "search", "embeddings", "chromadb"],
            "created_at": datetime.now().isoformat()
        }
    }
]

# Example project contexts for STPM
PROJECT_CONTEXTS = [
    {
        "id": "stpm_example_001",
        "document": "AI Coding Agent project context: Building a multi-service Docker application with FastAPI backend, React frontend, ChromaDB vector database, and Ollama AI integration. Focus on security, scalability, and maintainability.",
        "metadata": {
            "project_id": "ai-coding-agent-example",
            "context_type": "project_overview",
            "technologies": ["fastapi", "react", "docker", "chromadb", "ollama"],
            "phase": "development",
            "priority": "high",
            "created_at": datetime.now().isoformat()
        }
    },
    {
        "id": "stmp_example_002",
        "document": "Current development focus: Implementing infrastructure configuration files for nginx reverse proxy, SSL certificate management, monitoring with Prometheus and Grafana, and Docker scaling configurations.",
        "metadata": {
            "project_id": "ai-coding-agent-example",
            "context_type": "current_task",
            "phase": "infrastructure",
            "priority": "high",
            "tags": ["infrastructure", "nginx", "ssl", "monitoring", "scaling"],
            "created_at": datetime.now().isoformat()
        }
    }
]

class ChromaDBSeeder:
    """Seed ChromaDB collections with initial data."""
    
    def __init__(self):
        """Initialize the ChromaDB client."""
        self.client = chromadb.HttpClient(host=CHROMA_HOST, port=CHROMA_PORT)
        
    def seed_ltkb_documents(self) -> bool:
        """Seed the LTKB documents collection."""
        try:
            collection = self.client.get_collection("ltkb_documents")
            
            # Check if already seeded
            existing_count = collection.count()
            if existing_count > 0:
                logger.info(f"LTKB documents collection already has {existing_count} documents")
                return True
            
            # Add documents
            collection.add(
                documents=[doc["document"] for doc in LTKB_DOCUMENTS],
                metadatas=[doc["metadata"] for doc in LTKB_DOCUMENTS],
                ids=[doc["id"] for doc in LTKB_DOCUMENTS]
            )
            
            logger.info(f"Seeded {len(LTKB_DOCUMENTS)} LTKB documents")
            return True
            
        except Exception as e:
            logger.error(f"Failed to seed LTKB documents: {e}")
            return False
    
    def seed_code_patterns(self) -> bool:
        """Seed the code patterns collection."""
        try:
            collection = self.client.get_collection("ltkb_code_patterns")
            
            # Check if already seeded
            existing_count = collection.count()
            if existing_count > 0:
                logger.info(f"Code patterns collection already has {existing_count} patterns")
                return True
            
            # Add patterns
            collection.add(
                documents=[pattern["document"] for pattern in CODE_PATTERNS],
                metadatas=[pattern["metadata"] for pattern in CODE_PATTERNS],
                ids=[pattern["id"] for pattern in CODE_PATTERNS]
            )
            
            logger.info(f"Seeded {len(CODE_PATTERNS)} code patterns")
            return True
            
        except Exception as e:
            logger.error(f"Failed to seed code patterns: {e}")
            return False
    
    def seed_project_contexts(self) -> bool:
        """Seed the project context collection."""
        try:
            collection = self.client.get_collection("stpm_project_context")
            
            # Add example contexts (these can be overwritten)
            collection.add(
                documents=[ctx["document"] for ctx in PROJECT_CONTEXTS],
                metadatas=[ctx["metadata"] for ctx in PROJECT_CONTEXTS],
                ids=[ctx["id"] for ctx in PROJECT_CONTEXTS]
            )
            
            logger.info(f"Seeded {len(PROJECT_CONTEXTS)} project contexts")
            return True
            
        except Exception as e:
            logger.error(f"Failed to seed project contexts: {e}")
            return False
    
    def verify_seeding(self) -> Dict[str, int]:
        """Verify that seeding was successful."""
        counts = {}
        
        try:
            collections = ["ltkb_documents", "ltkb_code_patterns", "stpm_project_context"]
            
            for collection_name in collections:
                collection = self.client.get_collection(collection_name)
                counts[collection_name] = collection.count()
                logger.info(f"Collection '{collection_name}': {counts[collection_name]} documents")
            
        except Exception as e:
            logger.error(f"Failed to verify seeding: {e}")
        
        return counts

def main():
    """Main seeding function."""
    logger.info("Starting ChromaDB seeding for AI Coding Agent")
    
    # Initialize seeder
    seeder = ChromaDBSeeder()
    
    # Seed collections
    success_count = 0
    total_operations = 3
    
    if seeder.seed_ltkb_documents():
        success_count += 1
    
    if seeder.seed_code_patterns():
        success_count += 1
    
    if seeder.seed_project_contexts():
        success_count += 1
    
    # Verify seeding
    counts = seeder.verify_seeding()
    
    # Generate report
    report = {
        "timestamp": datetime.now().isoformat(),
        "operations_successful": success_count,
        "total_operations": total_operations,
        "collection_counts": counts,
        "status": "success" if success_count == total_operations else "partial_success"
    }
    
    # Save report
    report_path = Path(PERSIST_DIRECTORY) / "seeding_report.json"
    report_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Seeding completed. Report saved to: {report_path}")
    logger.info(f"Status: {report['status']}")
    
    return success_count == total_operations

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
